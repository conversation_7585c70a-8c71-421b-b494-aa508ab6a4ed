<template>
	<BaseCmsPage>
	<Body class="page-checkout-step3" />
		<WebshopCheckoutLayout class="wc-shipping-only">
			<template #header>
				<WebshopCheckoutHeader :step="3" />
			</template>

			<template #wcCol1>
				<BaseWebshopCheckout v-slot="{cart}">
					<WebshopStep :step="1" :completed="true" />
					<BaseWebshopShippingForm class="step3 step-form3 form form-animated-label ajax_siteform ajax_siteform_loading" v-slot="{loading, fields, onShippingUpdate, status, activeShipping}">
						<div v-if="status?.data?.errors">
							<div class="error global-error" v-for="error in status.data.errors" :key="error">{{ error.error }}</div>
						</div>

						<div class="shipping-method-cnt">
							<WebshopStep :step="2" :title="true" />
							<div class="section-shipping">
								<div v-for="field in fields" :key="field.value">
									<BaseFormField :item="field" v-slot="{errorMessage}">
										<BaseFormInput :id="field.code" :option-class="'shipping-row' + (field?.length <= 1 ? ' single-shipping' : '')">
											<template #default="{item}">
												<span @click="onShippingUpdate(item)">{{item.title}}</span>
												<div v-if="item.description" class="shipping_info" v-html="item.description"></div>
												<div class="shipping-data shipping_info shipping-info-parcels">
													<LazyBaseThemeWebshopGlsParcelLockers v-if="['gls_locker', 'gls_locker2'].includes(item.widget) && activeShipping.id == item.id" @select="onShippingUpdate(item, $event)" />
													<LazyBaseThemeWebshopBoxNowParcelLockers v-if="['boxnow_locker'].includes(item.widget) && activeShipping.id == item.id" @select="onShippingUpdate(item, $event)" />	
												</div>
											</template>
										</BaseFormInput>
										<span class="error" v-show="errorMessage" v-html="errorMessage" />

										<span class="shipping_price_info price">
											<BaseUtilsFormatCurrency v-if="field.shipping_price" :price="field.shipping_price" />
											<BaseCmsLabel v-else code="free"/>
										</span> 
									</BaseFormField>
								</div>
							</div>
							<button class="btn btn-primary btn-checkout-last" type="submit" :class="{'loading': loading}" :disabled="loading"><UiLoader v-if="loading" /><BaseCmsLabel code="goto_step3_button" tag="span" /></button>
						</div>
					</BaseWebshopShippingForm>

					<WebshopStep :step="3" />
					<WebshopStep :step="4" />

					<WebshopCheckoutTracking v-if="cart?.parcels[0]?.items?.length" step="3" :cart="cart" />
				</BaseWebshopCheckout>
			</template>
		</WebshopCheckoutLayout>
	</BaseCmsPage>
</template>

<script setup>
</script>

<style lang="less" scoped>
	.global-success,.global-error{margin-top: 15px; margin-bottom: 0;}
	.shipping-method-cnt{
		padding: 20px 0 40px;
		@media (max-width: @t){padding: 15px 0 35px;}
		@media (max-width: @tp){padding: 15px 0 30px;}
	}
	.section-shipping{
		padding-bottom: 15px;
		.global-error{display: none !important;}
		@media (max-width: @t){padding-bottom: 10px;}
		@media (max-width: @tp){padding-bottom: 10px;}
	}
	.shipping_info{
		font-size: 16px; line-height: 22px; padding-top: 3px; font-weight: 300; display: none;
		:deep(p){padding-bottom: 0;}
		@media (max-width: @t){font-size: 15px; line-height: 21px;}
		@media (max-width: @tp){font-size: 14px; line-height: 18px;}
	}
	:deep(.base-parcel-lockers-btn){
		text-decoration: underline; .transition(text-decoration-color);
		@media (min-width: @h){
			&:hover{text-decoration-color: transparent;}
		}
	}
	:deep(.shipping-row){
		margin-bottom: 10px; position: relative; display: block;
		input[type=radio] + label:before{width: 22px; height: 22px;}
		input[type=radio]+label{font-size: 18px !important; line-height: 24px !important; padding-right: 85px;}
		:deep(.error){padding-left: 0;}
		&.single-shipping{
			input[type=radio]+label{padding-left: 0;}
			input[type=radio]+label:before,input[type=radio]+label:after{display: none;}
		}
		input[type=radio]:checked + label{
			.shipping_info{display: block;}
		}
		@media (max-width: @t){
			margin-bottom: 8px;
			input[type=radio] + label:before{width: 20px; height: 20px;}
			input[type=radio]+label{font-size: 16px !important; line-height: 24px !important;}
			.shipping_price_info{font-size: 14px !important; line-height: 18px !important; top: 8px;}
		}
		@media (max-width: @tp){
			margin-bottom: 5px;
		}
		@media (max-width: @m){
			input[type=radio]+label{padding-right: 75px; font-size: 14px!important; line-height: 20px!important;}
		}
	}
	.shipping_price_info{position: absolute; right: 0; padding-left: 0; padding: 0; top: 6px; font-size: 16px !important; line-height: 20px !important; font-weight: normal;}
	.btn-checkout-last{
		margin: 0; min-width: 175px;
		@media (max-width: @t){margin: 5px 0 0 0; min-width: 180px;}
		@media (max-width: @m){min-width: 100%;}
	}
	.btn-change-address{display: none !important;}
</style>

<style lang="less">
	.page-checkout-step3{
		.wc-step1.completed-step{
			.step_link,.step_link2{padding-top: 0;}
		}
		.wc-subtitle{
			padding-bottom: 15px !important;
			@media (max-width: @t){padding-bottom: 12px !important;}
			@media (max-width: @tp){padding-bottom: 7px !important;}
			@media (max-width: @m){padding-bottom: 5px !important;}
		}
	}
</style>
